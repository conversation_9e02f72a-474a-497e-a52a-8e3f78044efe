package provider

import (
	"context"
	"database/sql"
	"log/slog"
	"os"

	"demo/core/bootstrap"

	"github.com/samber/do"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/sqlitedialect"
	"github.com/uptrace/bun/driver/sqliteshim"
)

func NewDBServiceProvider() *DBServiceProvider {
	return &DBServiceProvider{}
}

type DBServiceProvider struct{}

func (s *DBServiceProvider) Register(ctx context.Context, container bootstrap.Container) error {
	// Register database connection
	do.Provide(container.Injector(), func(i *do.Injector) (*bun.DB, error) {
		return s.createDatabase()
	})

	return nil
}

func (s *DBServiceProvider) Boot(ctx context.Context, container bootstrap.Container) error {
	// Get database instance to ensure it's properly initialized
	db, err := do.Invoke[*bun.DB](container.Injector())
	if err != nil {
		return err
	}

	// Test database connection
	if err := db.Ping(); err != nil {
		slog.Error("Failed to ping database", slog.Any("error", err))
		return err
	}

	slog.Info("Database connection established successfully")
	return nil
}

func (s *DBServiceProvider) Teardown(ctx context.Context, container bootstrap.Container) error {
	// Get database instance and close connection
	db, err := do.Invoke[*bun.DB](container.Injector())
	if err != nil {
		slog.Warn("Failed to get database instance during teardown", slog.Any("error", err))
		return nil // Don't fail teardown if we can't get the instance
	}

	if err := db.Close(); err != nil {
		slog.Error("Failed to close database connection", slog.Any("error", err))
		return err
	}

	slog.Info("Database connection closed successfully")
	return nil
}

// createDatabase creates and configures the database connection
func (s *DBServiceProvider) createDatabase() (*bun.DB, error) {
	// Get database path from environment variable, default to local SQLite file
	dbPath := os.Getenv("DATABASE_PATH")
	if dbPath == "" {
		dbPath = "./data/app.db"
	}

	// Ensure data directory exists
	if err := os.MkdirAll("./data", 0o755); err != nil {
		slog.Error("Failed to create data directory", slog.Any("error", err))
		return nil, err
	}

	// Open SQLite database
	sqldb, err := sql.Open(sqliteshim.ShimName, dbPath)
	if err != nil {
		slog.Error("Failed to open database", slog.String("path", dbPath), slog.Any("error", err))
		return nil, err
	}

	// Create Bun database instance with SQLite dialect
	db := bun.NewDB(sqldb, sqlitedialect.New())

	// Configure connection pool
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(25)

	slog.Info("Database configured", slog.String("path", dbPath), slog.String("dialect", "sqlite"))
	return db, nil
}
