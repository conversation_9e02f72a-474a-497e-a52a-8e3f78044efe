package provider

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"demo/api"
	"demo/core/bootstrap"

	"github.com/danielgtaylor/huma/v2"
	"github.com/danielgtaylor/huma/v2/adapters/humago"
	"github.com/danielgtaylor/huma/v2/humacli"
)

// HumaOptions for the CLI.
type HumaOptions struct {
	Port int `help:"Port to listen on" short:"p" default:"8888"`
}

type HumaServiceProvider struct {
	wg         sync.WaitGroup
	mux        *http.ServeMux
	cli        humacli.CLI
	server     *http.Server
	ctx        context.Context
	cancel     context.CancelFunc
	errChan    chan error
	shutdownCh chan os.Signal
}

func NewHumaServiceProvider() *HumaServiceProvider {
	mux := http.NewServeMux()
	ctx, cancel := context.WithCancel(context.Background())

	return &HumaServiceProvider{
		mux:        mux,
		ctx:        ctx,
		cancel:     cancel,
		err<PERSON>han:    make(chan error, 1),
		shutdownCh: make(chan os.Signal, 1),
	}
}

func (s *HumaServiceProvider) Register(ctx context.Context, container bootstrap.Container) error {
	cli := humacli.New(func(hooks humacli.Hooks, options *HumaOptions) {
		// Create a new mux & API using humago
		humaAPI := humago.NewWithPrefix(s.mux, "/api", huma.DefaultConfig("My API", "1.0.0"))
		api.Register(humaAPI)

		// Create HTTP server
		s.server = &http.Server{
			Addr:    fmt.Sprintf(":%d", options.Port),
			Handler: s.mux,
		}

		// Tell the CLI how to start your server.
		hooks.OnStart(func() {
			s.startServer()
		})
	})
	s.cli = cli

	// Set up signal handling for graceful shutdown
	signal.Notify(s.shutdownCh, os.Interrupt, syscall.SIGTERM, syscall.SIGINT)

	return nil
}

func (s *HumaServiceProvider) Boot(ctx context.Context, container bootstrap.Container) error {
	// Run the CLI in a goroutine to avoid blocking
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		defer func() {
			if r := recover(); r != nil {
				slog.Error("CLI panic recovered", slog.Any("panic", r))
				s.errChan <- fmt.Errorf("CLI panic: %v", r)
			}
		}()

		// Run the CLI. When passed no commands, it starts the server.
		s.cli.Run()
	}()

	// Start monitoring for shutdown signals
	s.wg.Add(1)
	go s.monitorShutdown()

	return nil
}

func (s *HumaServiceProvider) Teardown(ctx context.Context, container bootstrap.Container) error {
	slog.Info("Starting graceful shutdown of Huma service")

	// Cancel the context to signal shutdown
	s.cancel()

	// Stop signal notifications
	signal.Stop(s.shutdownCh)
	close(s.shutdownCh)

	// Shutdown the HTTP server with timeout
	if s.server != nil {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		if err := s.server.Shutdown(shutdownCtx); err != nil {
			slog.Error("Server shutdown error", slog.Any("error", err))
			// Force close if graceful shutdown fails
			if closeErr := s.server.Close(); closeErr != nil {
				slog.Error("Server force close error", slog.Any("error", closeErr))
			}
			return fmt.Errorf("server shutdown failed: %w", err)
		}
	}

	// Wait for all goroutines to finish with timeout
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		slog.Info("All goroutines finished successfully")
	case <-time.After(10 * time.Second):
		slog.Warn("Timeout waiting for goroutines to finish")
		return fmt.Errorf("timeout waiting for goroutines to finish")
	}

	// Close error channel
	close(s.errChan)

	slog.Info("Huma service shutdown completed")
	return nil
}

// startServer starts the HTTP server in a goroutine with error handling
func (s *HumaServiceProvider) startServer() {
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		defer func() {
			if r := recover(); r != nil {
				slog.Error("Server panic recovered", slog.Any("panic", r))
				s.errChan <- fmt.Errorf("server panic: %v", r)
			}
		}()

		slog.Info("Starting HTTP server", slog.String("addr", s.server.Addr))

		if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			slog.Error("Server failed to start", slog.Any("error", err))
			s.errChan <- fmt.Errorf("server failed: %w", err)
		}
	}()
}

// monitorShutdown monitors for shutdown signals and errors
func (s *HumaServiceProvider) monitorShutdown() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			slog.Info("Context cancelled, stopping shutdown monitor")
			return
		case sig := <-s.shutdownCh:
			if sig != nil {
				slog.Info("Received shutdown signal", slog.String("signal", sig.String()))
				s.cancel()
				return
			}
		case err := <-s.errChan:
			if err != nil {
				slog.Error("Received error from service", slog.Any("error", err))
				s.cancel()
				return
			}
		}
	}
}
