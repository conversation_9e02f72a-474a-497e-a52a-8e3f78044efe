# Copilot Instructions for go-example-prompt-ai

## Project Overview
This is a modular Go REST API project using [huma](https://github.com/danielgtaylor/huma) for endpoint registration and request/response modeling. The architecture is service-provider driven, with explicit bootstrapping and lifecycle management. API modules are versioned and domain-specific.

## Architecture & Structure
- **Service Provider Pattern**: All core and API services are implemented as providers in `internal/service/provider/`. Each provider implements `Register`, `Boot`, and `Teardown` for lifecycle control.
- **Bootstrapping**: The application is started via a bootstrapper (`core/bootstrap/`). Providers are registered and booted in order, and shut down gracefully on signal.
- **API Layer**: HTTP endpoints are defined under `api/v1/` (e.g., `greeting/greeting.go`). Handlers use `huma.Register` for route setup.
- **Domain Separation**: Each domain (e.g., greeting) has its own directory and input/output types. Follow this pattern for new features.
- **Versioning**: API versions are nested (`api/v1/`). Future versions should follow the same structure.
- **Request/Response Modeling**: Use Go structs with huma tags for path/query/body fields. Example:
  ```go
  type GreetingInput struct {
      Name string `path:"name" maxLength:"30" example:"world" doc:"Name to greet"`
  }
  ```
- **Output Structs**: Responses are wrapped in a `Body` struct for consistency.

## Developer Workflows
- **Build**: Run `go build ./...` from the project root.
- **Test**: Run `go test ./...` from the root (add tests in parallel to new features).
- **Run**: Start the app with `go run main.go` (handles graceful shutdown via signals).
- **Debug**: Use standard Go debugging tools. No custom scripts detected.

## Patterns & Conventions
- **Service Provider Lifecycle**: Implement `Register`, `Boot`, and `Teardown` for each provider. Register all providers in the bootstrapper (`main.go`).
- **Clean Architecture/DDD**: Prompts in `doc/prompts/` guide AI agents to generate code using Clean Architecture and Domain-Driven Design. Reference these for new CRUD features.
- **Prompt Templates**: Use `doc/prompts/generic-crud-template.md` for new entities. For products, use `products-usecase.md`.
- **Documentation**: API and use case documentation templates are in `doc/guidelines/`.
- **Validation**: Use huma struct tags for validation (e.g., `maxLength`, `example`).
- **Error Handling**: Return errors from handlers as the second return value.

## External Dependencies
- **huma**: Used for API registration and request/response modeling.
- **samber/do**: Used for dependency injection in service providers.

## Integration Points
- **Service Providers**: All core and API services are registered in the bootstrapper (`main.go`). See `internal/service/provider/` for examples.
- **API Registration**: All endpoints are registered via `huma.Register` in their respective domain files.
- **No database or external service integration detected** (add instructions if/when added).

## Example: Adding a New Service Provider or Endpoint
**Service Provider:**
1. Create a new provider in `internal/service/provider/` implementing `Register`, `Boot`, and `Teardown`.
2. Add the provider to the bootstrapper in `main.go`.

**API Endpoint:**
1. Create a new directory under `api/v1/` for the domain.
2. Define input/output structs with huma tags.
3. Register the endpoint using `huma.Register`.
4. Add documentation in `doc/prompts/` and `doc/guidelines/` as needed.

## References
- `main.go`: Application entrypoint and service provider bootstrapping
- `core/bootstrap/bootstrap.go`: Bootstrapping and lifecycle management
- `internal/service/provider/`: Service provider implementations
- `api/v1/greeting/greeting.go`: Example endpoint implementation
- `doc/prompts/README.md`: AI prompt usage and conventions
- `doc/prompts/generic-crud-template.md`: CRUD template for new entities

---

**Feedback Requested:**
Please review and suggest improvements or clarify any missing project-specific conventions, workflows, or integration details.
