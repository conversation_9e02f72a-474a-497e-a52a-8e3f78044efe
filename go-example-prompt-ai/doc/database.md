# Database Configuration

This project uses [Bun ORM](https://bun.uptrace.dev/) with SQLite as the default database.

## Configuration

The database is automatically configured and registered in the dependency injection container via the `CoreServiceProvider`.

### Environment Variables

- `DATABASE_PATH`: Path to the SQLite database file (default: `./data/app.db`)

### Examples

```bash
# Use default SQLite file
DATABASE_PATH=./data/app.db

# Use in-memory database (useful for testing)
DATABASE_PATH=:memory:

# Use different database for different environments
DATABASE_PATH=./data/development.db
DATABASE_PATH=./data/test.db
DATABASE_PATH=./data/production.db
```

## Usage

### Getting Database Instance

Use the helper functions in `core/database/database.go`:

```go
import (
    "demo/core/database"
    "github.com/samber/do"
)

// In a service or repository
func NewMyService(injector *do.Injector) *MyService {
    db, err := database.GetDB(injector)
    if err != nil {
        return nil, err
    }
    
    return &MyService{db: db}
}

// Or use MustGetDB if you want to panic on error
func NewMyService(injector *do.Injector) *MyService {
    db := database.MustGetDB(injector)
    return &MyService{db: db}
}
```

### Direct DI Container Usage

```go
import (
    "github.com/samber/do"
    "github.com/uptrace/bun"
)

func NewMyService(injector *do.Injector) (*MyService, error) {
    db, err := do.Invoke[*bun.DB](injector)
    if err != nil {
        return nil, err
    }
    
    return &MyService{db: db}, nil
}
```

## Database Features

- **SQLite by default**: No external database server required
- **Connection pooling**: Configured with reasonable defaults (25 max open/idle connections)
- **Graceful shutdown**: Database connections are properly closed during application shutdown
- **Environment-based configuration**: Easy to switch between different database files
- **In-memory support**: Use `:memory:` for testing scenarios

## File Structure

```
data/
├── app.db              # Default SQLite database file
├── development.db      # Development environment database
├── test.db            # Test environment database
└── production.db      # Production environment database
```

The `data/` directory is automatically created and is excluded from version control via `.gitignore`.

## Migration Support

For database migrations, follow the Bun migration patterns as documented in the project guidelines. Each domain should include its migrations in the `infra/bunrepo/migrations/` directory.

## Testing

For testing, you can use an in-memory database:

```bash
DATABASE_PATH=:memory: go test ./...
```

Or create a separate test database file:

```bash
DATABASE_PATH=./data/test.db go test ./...
```
