package main

import (
	"context"
	"log/slog"
	"os"
	"os/signal"
	"syscall"

	"demo/core/bootstrap"
	"demo/internal/service/provider"

	"github.com/samber/do"

	_ "github.com/danielgtaylor/huma/v2/formats/cbor"
)

func main() {
	ctx := context.Background()
	injector := do.New()

	// Create bootstrapper
	bootstrapper := bootstrap.NewDefaultBootStrapper(
		injector,
		provider.NewDBServiceProvider(),
		provider.NewHumaServiceProvider(),
	)

	// Register services
	if err := bootstrapper.Register(ctx); err != nil {
		slog.Error("Failed to register services", slog.Any("error", err))
		os.Exit(1)
	}

	// Boot services
	if err := bootstrapper.Boot(ctx); err != nil {
		slog.Error("Failed to boot services", slog.Any("error", err))
		os.Exit(1)
	}

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON>han, os.Interrupt, syscall.SIGTERM, syscall.SIGINT)

	slog.Info("Application started successfully. Press Ctrl+C to shutdown.")

	// Wait for shutdown signal
	sig := <-sigChan
	slog.Info("Received shutdown signal", slog.String("signal", sig.String()))

	// Perform graceful shutdown
	if err := bootstrapper.Shutdown(ctx); err != nil {
		slog.Error("Shutdown failed", slog.Any("error", err))
		os.Exit(1)
	}

	slog.Info("Application shutdown completed successfully")
}
